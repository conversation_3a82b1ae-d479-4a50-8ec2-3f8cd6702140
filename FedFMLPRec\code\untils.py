import re
import os
import gzip
import datetime
import numpy as np
import pandas as pd

import torch
from torch.utils.data import Dataset, DataLoader

def ensure_dir ( path ):
    """
    确保目录存在，如果不存在则创建
    path: 目录路径
    """
    if not os.path.exists (path):
        os.makedirs (path)


def get_local_time ( ):
    """
    获取当前本地时间，格式化为'月-日-年_时-分-秒'
    """
    cur = datetime.datetime.now ()
    cur = cur.strftime ('%b-%d-%Y_%H-%M-%S')

    return cur


def check_data_dir ( root_path, data_filename ):
    """
    检查数据文件是否存在
    root_path: 根目录路径
    data_filename: 数据文件名
    返回: 完整的数据文件路径
    """
    data_dir = root_path
    ensure_dir (data_dir)
    data_dir = os.path.join (data_dir, data_filename)
    assert os.path.exists (data_dir), f'{data_filename} in {root_path} not exists'
    return data_dir


def parse ( path ):
    """
    解析gzip压缩文件
    path: gzip文件路径
    """
    g = gzip.open (path, 'rb')
    for l in g:
        yield eval (l)

class Interactions (object):
    """
    交互数据处理类
    用于加载和处理用户-物品交互数据
    """

    def __init__ ( self, config, encoding = True ) -> None:
        """
        初始化Interactions
        config: 配置字典，包含数据集、最大序列长度等参数
        encoding: 是否对ID进行编码，默认为True
        """
        self.data = None
        self.user_num, self.item_num = None, None

        self.dataset = config ['dataset']
        self.max_len = config ['max_seq_len']
        self.uid_name = config ['uid_name']  # 用户ID列名
        self.iid_name = config ['iid_name']  # 物品ID列名
        self.inter_name = config ['inter_name']  # 交互值列名
        self.time_name = config ['time_name']  # 时间戳列名
        self.test_ratio = config ['test_ratio']  # 测试集比例，默认0.2
        self.sm = config ['split_method']  # 划分方法
        self.prepro = config ['prepro']  # 预处理方法

        self.encoding = encoding  # 是否对ID进行编码


    def _load_raw_data ( self ):
           """
           加载原始数据
           根据dataset参数加载不同的数据集
           """
           data_dir = None
           if self.dataset == 'ml-100k':
               self.data_dir = check_data_dir (f'../../movielens/{self.dataset}/', 'u.data')
               self.data = pd.read_csv (
                   self.data_dir,
                   delimiter = '\t',
                   names = [self.uid_name, self.iid_name, self.inter_name, self.time_name],
                   engine = 'python')

           elif self.dataset == 'ml-1m':
               # this dataset is only for toy-implementation
               self.data_dir = check_data_dir (f'../../movielens/{self.dataset}/', 'ratings.dat')
               self.data = pd.read_csv (
                   self.data_dir,
                   delimiter = '::',
                   names = [self.uid_name, self.iid_name, self.inter_name, self.time_name],
                   engine = 'python')

           elif self.dataset == 'ml-25m':
               self.data_dir = check_data_dir (f'../../movielens/{self.dataset}/', 'ratings.csv')
               self.data = pd.read_csv (self.data_dir)
               self.data.rename (
                   columns = {
                       'userId': self.uid_name,
                       'movieId': self.iid_name,
                       'rating': self.inter_name,
                       'timestamp': self.time_name},
                   inplace = True)

           elif self.dataset == 'music':
               self.data_dir = check_data_dir (f'../../amazon/', 'Digital_Music.csv')
               self.data = pd.read_csv (self.data_dir,
                                        names = [self.iid_name, self.uid_name, self.inter_name, self.time_name])
           elif self.dataset == 'video':
               self.data_dir = check_data_dir (f'../../amazon/', 'Video_Games.csv')
               self.data = pd.read_csv (self.data_dir,
                                        names = [self.iid_name, self.uid_name, self.inter_name, self.time_name])

           elif self.dataset == 'arts':
               self.data_dir = check_data_dir (f'../../amazon/', 'Arts_Crafts_and_Sewing.csv')
               self.data = pd.read_csv (self.data_dir,
                                        names = [self.iid_name, self.uid_name, self.inter_name, self.time_name])

           elif self.dataset == 'steam':
               self.data_dir = check_data_dir (f'../../steam/', 'steam_reviews.json.gz')

               i = 0
               df = {}
               for d in parse (self.data_dir):
                   df [i] = d
                   i += 1
               self.data = pd.DataFrame.from_dict (df, orient = 'index')
               self.data = self.data [['user_id', 'product_id', 'hours', 'date']].copy ()
               self.data = self.data [~self.data ['user_id'].isna ()].reset_index (drop = True)
               self.data.rename (
                   columns = {
                       'user_id': self.uid_name,
                       'product_id': self.iid_name,
                       'hours': self.inter_name,
                       'date': self.time_name},
                   inplace = True)
               self.data [self.inter_name] = self.data [self.inter_name].fillna (0.)

           elif self.dataset == 'retail':
               self.data_dir = check_data_dir (f'./retail/', 'events.csv')

               self.data = pd.read_csv (self.data_dir, header = 0, usecols = [0, 1, 2, 3],
                                        dtype = {0: np.int64, 1: np.int64, 2: str, 3: np.int64})
               self.data.columns = [self.time_name, self.uid_name, 'event', self.iid_name]
               self.data [self.time_name] = (self.data [self.time_name] / 1000).astype (int)
               self.data [self.time_name] = pd.to_datetime (self.data [self.time_name], unit = 's')
               self.data = self.data.query ('event == "view"').reset_index (drop = True)
               del self.data ['event']

           elif self.dataset == 'yoochoose':
               self.data_dir = check_data_dir (f'./yoochoose/', 'yoochoose-clicks.dat')
               self.data = pd.read_csv (
                   self.data_dir,
                   sep = ',', header = None, usecols = [0, 1, 2], dtype = {0: np.int32, 1: str, 2: np.int64},
                   names = [self.uid_name, self.time_name, self.iid_name]  # 'SessionId', 'time', 'ItemId'
               )
               self.data [self.time_name] = pd.to_datetime (self.data [self.time_name], format = '%Y-%m-%dT%H:%M:%S.%fZ')

           else:
               raise NameError (f'Invalid dataset name: {self.dataset}')

           # 删除重复的用户-物品交互，保留最后一次交互
           self.data.drop_duplicates ([self.uid_name, self.iid_name], keep = 'last', ignore_index = True)

    def _build_seq ( self ):
        """
        构建序列数据
        将用户的交互记录按时间排序，构建为序列
        """
        # 按用户ID和时间戳排序
        if self.time_name is not None:
            self.data.sort_values (by = [self.uid_name, self.time_name], ascending = True, inplace = True)
        else:
            self.data.sort_values (ny = [self.uid_name], ascending = True, inplace = True)

        self.data.reset_index (drop = True, inplace = True)

        # 获取用户ID和物品ID列
        uid_col_arr = self.data [self.uid_name].to_numpy ()
        iid_col_arr = self.data [self.iid_name].to_numpy ()

        last_uid = None

        '''
        uid_list: 用户ID列表 [usr_id1, usr_id2, ...]
        items_list: 物品ID列表，包含用户的交互序列 [[item1, item2, ...], [item3, item4, ...], ...]
        target_list: 目标物品ID列表 [target_item1, target_item2, ...]
        '''
        uid_list, items_list, target_list, item_list_length = [], [], [], []
        seq_start = 0

        # 构建用户序列
        for i, uid in enumerate (uid_col_arr):
            if last_uid != uid:
                # 新用户的开始
                last_uid = uid
                seq_start = i
            else:
                # 继续当前用户的序列
                if i - seq_start > self.max_len:
                    # 如果序列长度超过最大长度，则移动序列起始位置
                    seq_start += 1
                # 添加序列、目标和序列长度
                uid_list.append (uid)
                items_list.append (iid_col_arr [seq_start: i])
                target_list.append (iid_col_arr [i])
                item_list_length.append (i - seq_start)

        self.uid_list = np.array (uid_list)
        item_list_length = np.array (item_list_length, dtype = np.int64)

        new_length = len (items_list)  # 序列数量
        self.item_list_len = torch.tensor (item_list_length)
        # 创建固定长度的序列张量，用0填充
        new_item_list = torch.zeros ((new_length, self.max_len)).long ()

        # 将序列数据填充到张量中
        for i, (items, length) in enumerate (zip (items_list, item_list_length)):
            new_item_list [i] [:length] = torch.LongTensor (items)

        self.target_list = torch.LongTensor (target_list)
        self.new_item_list = new_item_list


    def _filter_core ( self ):
        """
        核心过滤
        过滤掉交互次数少于core_num的用户和物品
        """
        if self.prepro == 'raw':
            # 不进行过滤
            pass
        elif self.prepro.endswith ('core'):
            # 提取核心数
            pattern = re.compile (r'\d+')
            core_num = int (pattern.findall (self.prepro) [0])
            # 迭代过滤，直到所有用户和物品的交互次数都达到要求
            while True:
                user_item_counts = self.data.groupby ('user') ['item'].transform ('count')
                item_user_counts = self.data.groupby ('item') ['user'].transform ('count')

                filtered_df = self.data [
                    (user_item_counts >= core_num) & (item_user_counts >= core_num)
                    ]

                if len (filtered_df) < len (self.data):
                    self.data = filtered_df
                else:
                    break
        else:
            raise ValueError ('Invalid prepro value...')

        self.data.reset_index (drop = True, inplace = True)

    def _encode_id ( self ):
        """
        对用户ID和物品ID进行编码
        将原始ID映射为从1开始的连续整数（0用于填充）
        """
        # 获取唯一的用户ID和物品ID
        token_uid = pd.Categorical (self.data [self.uid_name]).categories.to_numpy ()
        token_iid = pd.Categorical (self.data [self.iid_name]).categories.to_numpy ()
        # 从1开始编码，0留给none-item
        self.token_uid = {token + 1: uid for token, uid in enumerate (token_uid)}
        self.token_iid = {token + 1: iid for token, iid in enumerate (token_iid)}

        # 反向映射
        self.uid_token = {v: k for k, v in self.token_uid.items ()}
        self.iid_token = {v: k for k, v in self.token_iid.items ()}

        # 用编码后的ID替换原始ID
        self.data [self.uid_name] = self.data [self.uid_name].map (self.uid_token)
        self.data [self.iid_name] = self.data [self.iid_name].map (self.iid_token)

        # 更新用户数和物品数
        self.user_num = self.data [self.uid_name].nunique ()
        self.item_num = self.data [self.iid_name].nunique ()


    def _grouped_index ( self, group_by_list ):
        """
        按照group_by_list分组，返回每组的索引
        group_by_list: 用于分组的列表
        """
        index = {}
        for i, key in enumerate (group_by_list):
            if key not in index:
                index [key] = [i]
            else:
                index [key].append (i)
        return index.values ()

    def _build_dataset ( self, session = True ):
        """
        构建训练集和测试集
        session: 是否按会话构建，默认为True
        """
        train_idx, test_idx = [], []
        if self.sm == 'ufo':
            # 按用户划分训练集和测试集
            print ('fold-out by user')
            grouped_index = self._grouped_index (self.uid_list)
            for grouped_ids in grouped_index:
                total_cnt = len (grouped_ids)
                # 按比例划分
                split_ids = int (total_cnt * (1 - self.test_ratio))
                train_idx.extend (grouped_ids [0:split_ids])
                test_idx.extend (grouped_ids [split_ids:total_cnt])
        elif self.sm == 'fo':
            # 随机划分训练集和测试集
            print ('random fold-out')
            rnd_idx = torch.randperm (len (self.target_list))
            split_point = int (len (self.target_list) * (1 - self.test_ratio))
            train_idx, test_idx = rnd_idx [:split_point], rnd_idx [split_point:]
        else:
            raise ValueError (f'Invalid train test split method: {self.sm}')

        # 根据索引构建训练集和测试集
        if session:
            # 不包含用户ID的数据格式
            self.train_data = [self.new_item_list [train_idx], self.target_list [train_idx],
                               self.item_list_len [train_idx]]
            self.test_data = [self.new_item_list [test_idx], self.target_list [test_idx], self.item_list_len [test_idx]]
        else:
            # 包含用户ID的数据格式
            self.train_data = [self.uid_list [train_idx], self.new_item_list [train_idx], self.target_list [train_idx],
                               self.item_list_len [train_idx]]
            self.test_data = [self.uid_list [test_idx], self.new_item_list [test_idx], self.target_list [test_idx],
                              self.item_list_len [test_idx]]


    def build ( self ):
        """
        构建数据的完整流程
        包括加载原始数据、过滤、编码、构建序列和划分数据集
        """
        self._load_raw_data ()
        print ('Finish load raw data')
        self._filter_core ()
        print (f'Finish {self.prepro} processing')
        self._encode_id ()
        print (f'Finish re-encoding iid and uid')

        self._build_seq ()
        print (f'Finish building sequences from original data')
        self._build_dataset ()
        print ('Finish load data')

    def get_dataloader ( ds, batch_size, shuffle, num_workers = 4 ):
        """
        获取数据加载器
        ds: 数据集
        batch_size: 批次大小
        shuffle: 是否打乱数据
        num_workers: 工作进程数，默认为4
        """
        return DataLoader (ds, batch_size = batch_size, shuffle = shuffle, num_workers = num_workers)


class SequentialDataset (Dataset):
    """
    序列数据集类
    用于SASRec模型的数据加载
    """

    def __init__ ( self, data ):
        """
        初始化序列数据集
        data: 包含序列、下一个物品和序列长度的列表
        """
        super (SequentialDataset, self).__init__ ()
        self.seqs = data [0]  # 物品序列
        self.next_item = data [1]  # 下一个物品（目标）
        self.seq_lens = data [2]  # 序列长度

    def __len__ ( self ):
        """
        返回数据集大小
        """
        return len (self.next_item)

    def __getitem__ ( self, index ):
        """
        获取指定索引的样本
        index: 样本索引
        返回: (序列, 下一个物品, 序列长度)
        """
        return self.seqs [index], self.next_item [index], self.seq_lens [index]


class Logger:
    """
    日志记录器
    用于记录训练过程中的各种信息
    """

    def __init__ ( self, config, desc = None ):
        """
        初始化Logger
        config: 配置字典
        desc: 描述信息
        """
        log_root = config ['log_path']
        # 确保日志目录存在
        ensure_dir (log_root)

        current_time = get_local_time().replace('-', '').replace('_', '').replace(':', '')
        
        logfilename = f'{config["dataset"]}_len({config ["max_seq_len"]})_{current_time}.log'
        logfilepath = os.path.join (log_root, logfilename)

        self.filename = logfilepath

        # 写入配置信息
        f = open (logfilepath, 'w', encoding = 'utf-8')
        f.write (str (config) + '\n')
        f.flush ()
        f.close ()

    def info ( self, s = None ):
        """
        记录信息到日志文件
        s: 要记录的信息
        """
        print (s)
        f = open (self.filename, 'a', encoding = 'utf-8')
        f.write (f'[{get_local_time ()}] - {s}\n')
        f.flush ()
        f.close ()




