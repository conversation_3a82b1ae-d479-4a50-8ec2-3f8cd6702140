{'early_stop_enabled': True, 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'hidden_act': 'gelu'}
[Aug-01-2025_16-44-56] - 开始训练，配置参数如下：
[Aug-01-2025_16-44-56] - early_stop_enabled: True
[Aug-01-2025_16-44-56] - lr: 0.001
[Aug-01-2025_16-44-56] - batch_size: 128
[Aug-01-2025_16-44-56] - neg_num: 99
[Aug-01-2025_16-44-56] - l2_reg: 0
[Aug-01-2025_16-44-56] - l2_emb: 0.0
[Aug-01-2025_16-44-56] - embed_dim: 50
[Aug-01-2025_16-44-56] - hidden_size: 32
[Aug-01-2025_16-44-56] - dropout: 0.2
[Aug-01-2025_16-44-56] - epochs: 1000000
[Aug-01-2025_16-44-56] - early_stop: 50
[Aug-01-2025_16-44-56] - datapath: ../../data/
[Aug-01-2025_16-44-56] - dataset: ml-100k
[Aug-01-2025_16-44-56] - train_data: ml-100k.txt
[Aug-01-2025_16-44-56] - log_path: ../log
[Aug-01-2025_16-44-56] - num_layers: 2
[Aug-01-2025_16-44-56] - num_heads: 1
[Aug-01-2025_16-44-56] - inner_size: 256
[Aug-01-2025_16-44-56] - max_seq_len: 200
[Aug-01-2025_16-44-56] - upload_mode: full
[Aug-01-2025_16-44-56] - skip_test_eval: True
[Aug-01-2025_16-44-56] - eval_freq: 1
[Aug-01-2025_16-44-56] - use_dynamic_sampling: False
[Aug-01-2025_16-44-56] - norm_first: False
[Aug-01-2025_16-44-56] - hidden_act: gelu
[Aug-01-2025_16-44-56] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-01-2025_16-44-56] - 最大序列长度: 200
[Aug-01-2025_16-44-56] - 批次大小: 128
[Aug-01-2025_16-44-58] - 参数上传模式: full
[Aug-01-2025_16-44-58] - 隐私参数（本地更新）: []
[Aug-01-2025_16-44-58] - 非隐私参数（服务器聚合）: ['model.item_embeddings.weight', 'model.position_embeddings.weight', 'model.LayerNorm.weight', 'model.LayerNorm.bias', 'model.item_encoder.layer.0.filterlayer.complex_weight', 'model.item_encoder.layer.0.filterlayer.LayerNorm.weight', 'model.item_encoder.layer.0.filterlayer.LayerNorm.bias', 'model.item_encoder.layer.0.intermediate.dense_1.weight', 'model.item_encoder.layer.0.intermediate.dense_1.bias', 'model.item_encoder.layer.0.intermediate.dense_2.weight', 'model.item_encoder.layer.0.intermediate.dense_2.bias', 'model.item_encoder.layer.0.intermediate.LayerNorm.weight', 'model.item_encoder.layer.0.intermediate.LayerNorm.bias', 'model.item_encoder.layer.1.filterlayer.complex_weight', 'model.item_encoder.layer.1.filterlayer.LayerNorm.weight', 'model.item_encoder.layer.1.filterlayer.LayerNorm.bias', 'model.item_encoder.layer.1.intermediate.dense_1.weight', 'model.item_encoder.layer.1.intermediate.dense_1.bias', 'model.item_encoder.layer.1.intermediate.dense_2.weight', 'model.item_encoder.layer.1.intermediate.dense_2.bias', 'model.item_encoder.layer.1.intermediate.LayerNorm.weight', 'model.item_encoder.layer.1.intermediate.LayerNorm.bias']
[Aug-01-2025_16-44-58] - 动态负采样: False
[Aug-01-2025_16-44-58] - 用户数量: 943
[Aug-01-2025_16-44-58] - 物品数量: 1349
