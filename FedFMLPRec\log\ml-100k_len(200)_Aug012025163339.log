{'early_stop_enabled': True, 'lr': 0.001, 'batch_size': 128, 'neg_num': 99, 'l2_reg': 0, 'l2_emb': 0.0, 'embed_dim': 50, 'hidden_size': 32, 'dropout': 0.2, 'epochs': 1000000, 'early_stop': 50, 'datapath': '../../data/', 'dataset': 'ml-100k', 'train_data': 'ml-100k.txt', 'log_path': '../log', 'num_layers': 2, 'num_heads': 1, 'inner_size': 256, 'max_seq_len': 200, 'upload_mode': 'full', 'skip_test_eval': True, 'eval_freq': 1, 'use_dynamic_sampling': False, 'norm_first': False, 'hidden_act': 'gelu'}
[Aug-01-2025_16-33-39] - 开始训练，配置参数如下：
[Aug-01-2025_16-33-39] - early_stop_enabled: True
[Aug-01-2025_16-33-39] - lr: 0.001
[Aug-01-2025_16-33-39] - batch_size: 128
[Aug-01-2025_16-33-39] - neg_num: 99
[Aug-01-2025_16-33-39] - l2_reg: 0
[Aug-01-2025_16-33-39] - l2_emb: 0.0
[Aug-01-2025_16-33-39] - embed_dim: 50
[Aug-01-2025_16-33-39] - hidden_size: 32
[Aug-01-2025_16-33-39] - dropout: 0.2
[Aug-01-2025_16-33-39] - epochs: 1000000
[Aug-01-2025_16-33-39] - early_stop: 50
[Aug-01-2025_16-33-39] - datapath: ../../data/
[Aug-01-2025_16-33-39] - dataset: ml-100k
[Aug-01-2025_16-33-39] - train_data: ml-100k.txt
[Aug-01-2025_16-33-39] - log_path: ../log
[Aug-01-2025_16-33-39] - num_layers: 2
[Aug-01-2025_16-33-39] - num_heads: 1
[Aug-01-2025_16-33-39] - inner_size: 256
[Aug-01-2025_16-33-39] - max_seq_len: 200
[Aug-01-2025_16-33-39] - upload_mode: full
[Aug-01-2025_16-33-39] - skip_test_eval: True
[Aug-01-2025_16-33-39] - eval_freq: 1
[Aug-01-2025_16-33-39] - use_dynamic_sampling: False
[Aug-01-2025_16-33-39] - norm_first: False
[Aug-01-2025_16-33-39] - hidden_act: gelu
[Aug-01-2025_16-33-39] - 训练数据: ../../data/ml-100k/ml-100k.txt
[Aug-01-2025_16-33-39] - 最大序列长度: 200
[Aug-01-2025_16-33-39] - 批次大小: 128
[Aug-01-2025_16-33-41] - 参数上传模式: full
[Aug-01-2025_16-33-41] - 隐私参数（本地更新）: []
[Aug-01-2025_16-33-41] - 非隐私参数（服务器聚合）: ['item_embedding.weight', 'position_embedding.weight', 'layernorm.weight', 'layernorm.bias', 'item_encoder.layer.0.filterlayer.complex_weight', 'item_encoder.layer.0.filterlayer.layernorm.weight', 'item_encoder.layer.0.filterlayer.layernorm.bias', 'item_encoder.layer.0.intermediate.dense_1.weight', 'item_encoder.layer.0.intermediate.dense_1.bias', 'item_encoder.layer.0.intermediate.dense_2.weight', 'item_encoder.layer.0.intermediate.dense_2.bias', 'item_encoder.layer.0.intermediate.layernorm.weight', 'item_encoder.layer.0.intermediate.layernorm.bias', 'item_encoder.layer.1.filterlayer.complex_weight', 'item_encoder.layer.1.filterlayer.layernorm.weight', 'item_encoder.layer.1.filterlayer.layernorm.bias', 'item_encoder.layer.1.intermediate.dense_1.weight', 'item_encoder.layer.1.intermediate.dense_1.bias', 'item_encoder.layer.1.intermediate.dense_2.weight', 'item_encoder.layer.1.intermediate.dense_2.bias', 'item_encoder.layer.1.intermediate.layernorm.weight', 'item_encoder.layer.1.intermediate.layernorm.bias']
[Aug-01-2025_16-33-41] - 动态负采样: False
[Aug-01-2025_16-33-41] - 用户数量: 943
[Aug-01-2025_16-33-41] - 物品数量: 1349
