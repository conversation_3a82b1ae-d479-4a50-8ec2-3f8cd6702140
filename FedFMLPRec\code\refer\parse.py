import argparse
parser = argparse.ArgumentParser()
parser.add_argument("--hidden_size", default=64, type=int, help="hidden size of model")
parser.add_argument("--num_hidden_layers", default=2, type=int, help="number of filter-enhanced blocks")
parser.add_argument("--num_attention_heads", default=2, type=int)
parser.add_argument("--hidden_act", default="gelu", type=str) # gelu relu
parser.add_argument("--attention_probs_dropout_prob", default=0.5, type=float)
parser.add_argument("--hidden_dropout_prob", default=0.5, type=float)
parser.add_argument("--initializer_range", default=0.02, type=float)
parser.add_argument("--max_seq_length", default=50, type=int)
parser.add_argument("--no_filters", action="store_true", help="if no filters, filter layers transform to self-attention")
parser.add_argument ("--weight_decay", default = 0.0, type = float, help = "weight_decay of adam")
parser.add_argument ("--adam_beta1", default = 0.9, type = float, help = "adam first beta value")
parser.add_argument ("--adam_beta2", default = 0.999, type = float, help = "adam second beta value")
parser.add_argument ("--variance", default = 5, type = float)
